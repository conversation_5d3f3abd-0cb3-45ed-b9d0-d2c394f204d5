/* Member Filter Component Styles */

/* Fade In Animation */
.fade-in {
  animation: fadeIn 0.5s ease-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Content Blur Effect */
.content-blur {
  filter: blur(3px);
  pointer-events: none;
}

/* Filter Card Styles */
.filter-card {
  margin-bottom: 1.5rem;
  transition: all 0.3s ease;
}

.filter-card:hover {
  transform: translateY(-5px);
}

.filter-section {
  margin-bottom: 1.5rem;
  padding-bottom: 1.5rem;
  border-bottom: 1px solid rgba(0, 0, 0, 0.05);
}

.filter-section:last-child {
  border-bottom: none;
  padding-bottom: 0;
}

.filter-title {
  font-weight: 600;
  color: var(--primary);
  margin-bottom: 1rem;
  display: flex;
  align-items: center;
}

/* Modern Radio Buttons */
.modern-radio-group {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
}

.modern-radio {
  position: relative;
  display: flex;
  align-items: center;
}

.modern-radio-input {
  position: absolute;
  opacity: 0;
  cursor: pointer;
}

.modern-radio-label {
  display: flex;
  align-items: center;
  cursor: pointer;
  font-size: 0.95rem;
  margin-bottom: 0;
  transition: all 0.2s ease;
}

.radio-icon {
  display: inline-block;
  width: 18px;
  height: 18px;
  border-radius: 50%;
  border: 2px solid var(--secondary);
  margin-right: 0.75rem;
  position: relative;
  transition: all 0.2s ease;
}

.radio-icon:after {
  content: '';
  position: absolute;
  display: none;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 10px;
  height: 10px;
  border-radius: 50%;
  background: var(--primary);
}

.modern-radio-input:checked ~ .modern-radio-label .radio-icon {
  border-color: var(--primary);
}

.modern-radio-input:checked ~ .modern-radio-label .radio-icon:after {
  display: block;
}

.modern-radio-input:focus ~ .modern-radio-label .radio-icon {
  box-shadow: 0 0 0 3px var(--primary-light);
}

.modern-radio-label:hover {
  color: var(--primary);
}

/* Disabled state for radio buttons */
.modern-radio-input:disabled ~ .modern-radio-label {
  opacity: 0.5;
  cursor: not-allowed;
  pointer-events: none;
}

.modern-radio-label.disabled {
  opacity: 0.5;
  cursor: not-allowed;
  pointer-events: none;
}

.modern-radio-input:disabled ~ .modern-radio-label .radio-icon {
  border-color: #ccc;
  background-color: #f5f5f5;
}

.modern-radio-input:disabled:checked ~ .modern-radio-label .radio-icon {
  border-color: #ccc;
}

.modern-radio-input:disabled:checked ~ .modern-radio-label .radio-icon:after {
  background: #ccc;
}

/* Branch Container & Accordion Styles */
.branch-container {
  margin-bottom: 0.5rem;
}

.branch-radio {
  margin-bottom: 0;
}

.branch-label {
  position: relative;
  padding-right: 2rem;
  font-weight: 500;
}

.expand-icon {
  position: absolute;
  right: 0;
  top: 50%;
  transform: translateY(-50%);
  transition: transform 0.3s ease;
  color: var(--secondary);
  font-size: 0.8rem;
}

.expand-icon.expanded {
  transform: translateY(-50%) rotate(180deg);
  color: var(--primary);
}

/* Package Filters Accordion */
.package-filters {
  max-height: 0;
  overflow: hidden;
  transition: max-height 0.3s ease, padding 0.3s ease;
  margin-left: 2rem;
  border-left: 2px solid var(--border-color);
}

.package-filters.expanded {
  max-height: 400px;
  padding: 0.75rem 0 0.75rem 1rem;
  overflow-y: auto;
}

/* Custom Scrollbar for Package Filters */
.package-filters.expanded::-webkit-scrollbar {
  width: 6px;
}

.package-filters.expanded::-webkit-scrollbar-track {
  background: rgba(0, 0, 0, 0.05);
  border-radius: 3px;
}

.package-filters.expanded::-webkit-scrollbar-thumb {
  background: var(--primary);
  border-radius: 3px;
  opacity: 0.7;
}

.package-filters.expanded::-webkit-scrollbar-thumb:hover {
  background: var(--primary-dark);
  opacity: 1;
}

.package-list {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.package-item {
  position: relative;
}

/* Modern Checkbox Styles */
.modern-checkbox {
  position: relative;
  display: flex;
  align-items: center;
}

.modern-checkbox-input {
  position: absolute;
  opacity: 0;
  cursor: pointer;
}

.modern-checkbox-label {
  display: flex;
  align-items: center;
  cursor: pointer;
  font-size: 0.9rem;
  margin-bottom: 0;
  transition: all 0.2s ease;
  padding: 0.25rem 0;
}

.checkbox-icon {
  display: inline-block;
  width: 16px;
  height: 16px;
  border-radius: 3px;
  border: 2px solid var(--secondary);
  margin-right: 0.5rem;
  position: relative;
  transition: all 0.2s ease;
}

.checkbox-icon:after {
  content: '';
  position: absolute;
  display: none;
  top: 1px;
  left: 4px;
  width: 4px;
  height: 8px;
  border: solid white;
  border-width: 0 2px 2px 0;
  transform: rotate(45deg);
}

.modern-checkbox-input:checked ~ .modern-checkbox-label .checkbox-icon {
  background-color: var(--primary);
  border-color: var(--primary);
}

.modern-checkbox-input:checked ~ .modern-checkbox-label .checkbox-icon:after {
  display: block;
}

.modern-checkbox-input:focus ~ .modern-checkbox-label .checkbox-icon {
  box-shadow: 0 0 0 2px var(--primary-light);
}

.modern-checkbox-label:hover {
  color: var(--primary);
}

.modern-checkbox-label:hover .checkbox-icon {
  border-color: var(--primary);
}

/* Package Search Actions */
.package-search-actions {
  margin-top: 1rem;
  padding-top: 0.75rem;
  border-top: 1px solid var(--border-color);
  display: flex;
  gap: 0.5rem;
  flex-wrap: wrap;
}

.search-btn {
  flex: 1;
  min-width: 120px;
  font-weight: 500;
  transition: all 0.3s ease;
}

.search-btn:hover:not(:disabled) {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(var(--primary-rgb), 0.3);
}

.search-btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.package-search-actions .modern-btn-outline-secondary {
  border-color: var(--border-color);
  color: var(--text-secondary);
}

.package-search-actions .modern-btn-outline-secondary:hover:not(:disabled) {
  background-color: var(--bg-tertiary);
  border-color: var(--text-secondary);
}

/* Member List Card */
.member-list-card {
  height: 100%;
}

/* Modern Search Input */
.search-container {
  position: relative;
  width: 300px;
}

.modern-search-input {
  position: relative;
  display: flex;
  align-items: center;
}

.modern-search-input input {
  width: 100%;
  padding: 0.75rem 1rem 0.75rem 2.5rem;
  border: none;
  border-radius: var(--border-radius-md);
  background-color: rgba(0, 0, 0, 0.03);
  transition: all 0.3s ease;
  font-size: 0.95rem;
}

.modern-search-input input:focus {
  outline: none;
  background-color: rgba(0, 0, 0, 0.05);
  box-shadow: 0 0 0 3px var(--primary-light);
}

.search-icon {
  position: absolute;
  left: 0.75rem;
  color: var(--secondary);
  font-size: 0.95rem;
  pointer-events: none;
}

/* Table Container */
.table-container {
  overflow-x: auto;
  margin-bottom: 1.5rem;
  border-radius: var(--border-radius-md);
}

/* Member Name with Avatar */
.member-name {
  display: flex;
  align-items: center;
  gap: 0.75rem;
}

/* Remaining Days Styling */
.remaining-days {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-weight: 500;
}

/* Action Buttons */
.action-buttons {
  display: flex;
  justify-content: center;
  gap: 0.5rem;
}

/* Pagination Container */
.pagination-container {
  display: flex;
  justify-content: center;
  margin-top: 1.5rem;
}

/* Gender Chart Card */
.gender-chart-card {
  margin-bottom: 1.5rem;
  transition: all 0.3s ease;
}

.gender-chart-card:hover {
  transform: translateY(-5px);
}

.gender-chart-card .modern-card-body {
  padding: 1rem;
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 220px;
}

/* Dark Mode Support */
[data-theme="dark"] .filter-section {
  border-bottom-color: rgba(255, 255, 255, 0.1);
}

[data-theme="dark"] .modern-search-input input {
  background-color: rgba(255, 255, 255, 0.05);
  color: var(--text-primary);
}

[data-theme="dark"] .modern-search-input input:focus {
  background-color: rgba(255, 255, 255, 0.1);
}

[data-theme="dark"] .gender-chart-container {
  background-color: rgba(255, 255, 255, 0.05);
}

[data-theme="dark"] .modern-card {
  background-color: #2d3748;
  color: #e2e8f0;
}

[data-theme="dark"] .modern-card-header {
  border-bottom-color: #4a5568;
}

[data-theme="dark"] .modern-table th {
  background-color: #4a5568;
  color: #e2e8f0;
}

[data-theme="dark"] .modern-table td {
  color: #e2e8f0;
  border-bottom-color: #4a5568;
}

[data-theme="dark"] .modern-table tbody tr:hover {
  background-color: rgba(255, 255, 255, 0.05);
}

[data-theme="dark"] .modern-pagination .modern-page-link {
  background-color: #2d3748;
  border-color: #4a5568;
  color: #e2e8f0;
}

[data-theme="dark"] .modern-pagination .modern-page-link:hover {
  background-color: #4a5568;
}

[data-theme="dark"] .modern-pagination .modern-page-item.disabled .modern-page-link {
  background-color: #2d3748;
  color: #718096;
}

/* Dark Mode - Package Filters */
[data-theme="dark"] .package-filters {
  border-left-color: rgba(255, 255, 255, 0.1);
}

/* Dark Mode - Custom Scrollbar */
[data-theme="dark"] .package-filters.expanded::-webkit-scrollbar-track {
  background: rgba(255, 255, 255, 0.1);
}

[data-theme="dark"] .package-filters.expanded::-webkit-scrollbar-thumb {
  background: var(--primary);
  opacity: 0.8;
}

[data-theme="dark"] .package-filters.expanded::-webkit-scrollbar-thumb:hover {
  background: var(--primary-light);
  opacity: 1;
}

[data-theme="dark"] .checkbox-icon {
  border-color: #718096;
}

[data-theme="dark"] .modern-checkbox-input:checked ~ .modern-checkbox-label .checkbox-icon {
  background-color: var(--primary);
  border-color: var(--primary);
}

[data-theme="dark"] .modern-checkbox-label:hover {
  color: var(--primary);
}

[data-theme="dark"] .modern-checkbox-label:hover .checkbox-icon {
  border-color: var(--primary);
}

[data-theme="dark"] .expand-icon {
  color: #718096;
}

[data-theme="dark"] .expand-icon.expanded {
  color: var(--primary);
}

/* Dark Mode - Package Search Actions */
[data-theme="dark"] .package-search-actions {
  border-top-color: rgba(255, 255, 255, 0.1);
}

[data-theme="dark"] .package-search-actions .modern-btn-outline-secondary {
  border-color: rgba(255, 255, 255, 0.2);
  color: #adb5bd;
}

[data-theme="dark"] .package-search-actions .modern-btn-outline-secondary:hover:not(:disabled) {
  background-color: rgba(255, 255, 255, 0.1);
  border-color: #adb5bd;
}

/* Responsive Adjustments */
@media (max-width: 767.98px) {
  .modern-card-header {
    flex-direction: column;
    align-items: flex-start;
  }
  
  .search-container {
    width: 100%;
    margin-top: 1rem;
  }
  
  .action-buttons {
    flex-direction: column;
  }
  
  .action-buttons button {
    width: 100%;
    margin-left: 0 !important;
    margin-top: 0.5rem;
  }
  
  .action-buttons button:first-child {
    margin-top: 0;
  }
  
  .member-name {
    flex-direction: column;
    align-items: flex-start;
    gap: 0.5rem;
  }
}

/* Pagination Styles */
.pagination-wrapper {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 1.5rem;
  flex-wrap: wrap;
  gap: 1rem;
}

.pagination-info {
  color: var(--text-secondary);
  font-size: 0.9rem;
}

.pagination-controls {
  display: flex;
  align-items: center;
  gap: 1.5rem;
  flex-wrap: wrap;
}

.page-size-selector {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.page-size-selector .form-label {
  font-size: 0.9rem;
  color: var(--text-secondary);
  white-space: nowrap;
  margin-bottom: 0;
}

.page-size-selector select {
  background-color: var(--bg-primary);
  border: 1px solid var(--border-color);
  color: var(--text-primary);
  border-radius: var(--border-radius-sm);
  padding: 0.25rem 0.5rem;
  font-size: 0.875rem;
  min-width: 70px;
}

.page-size-selector select:focus {
  border-color: var(--primary);
  box-shadow: 0 0 0 0.2rem var(--primary-light);
  outline: 0;
}

.page-size-selector select option {
  background-color: var(--bg-primary);
  color: var(--text-primary);
}

/* Dark mode için pagination dropdown */
[data-theme="dark"] .page-size-selector select {
  background-color: var(--bg-tertiary);
  border-color: var(--border-color);
  color: var(--text-primary);
}

[data-theme="dark"] .page-size-selector select option {
  background-color: var(--bg-tertiary);
  color: var(--text-primary);
}

/* Mobile pagination */
@media (max-width: 768px) {
  .pagination-wrapper {
    flex-direction: column;
    align-items: stretch;
    gap: 1rem;
  }

  .pagination-controls {
    justify-content: center;
    gap: 1rem;
  }

  .pagination-info {
    text-align: center;
  }
}
  